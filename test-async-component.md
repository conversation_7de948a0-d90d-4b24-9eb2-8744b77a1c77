# 异步图片转素描组件测试指南

## 修改总结

已成功将 `src/components/imagetosketch/index.tsx` 组件从同步模式修改为异步轮询模式：

### 主要修改内容

1. **Task 接口更新**
   - 新增 `pending` 状态支持
   - 添加 `taskId` 字段用于存储服务器返回的任务ID
   - 添加 `message` 字段用于显示任务状态消息

2. **handleGenerate 函数重构**
   - 修改为异步提交模式，立即返回 taskId
   - 初始任务状态设为 `pending`
   - 提交成功后开始轮询任务状态

3. **新增 pollTaskStatus 函数**
   - 实现轮询逻辑，每2秒查询一次任务状态
   - 最多轮询150次（5分钟超时）
   - 支持任务状态实时更新
   - 处理完成、错误和超时情况

4. **UI 状态显示增强**
   - 新增 `pending` 状态的视觉效果（黄色主题）
   - 添加任务消息和进度显示
   - 更新状态图标和文本支持

5. **错误处理改进**
   - 网络错误时延长重试间隔
   - 轮询超时处理
   - 任务失败状态处理

## 测试步骤

### 1. 基本功能测试
1. 上传1-3张图片
2. 选择素描风格
3. 点击生成按钮
4. 验证任务立即显示为 "Pending" 状态
5. 观察状态变化：Pending → Generating → Completed

### 2. 轮询功能测试
1. 提交任务后，检查浏览器网络面板
2. 验证每2秒发送一次状态查询请求到 `/api/image-gen/sketch/status`
3. 确认轮询在任务完成后停止

### 3. 错误处理测试
1. 测试网络断开情况下的重试机制
2. 测试服务器错误响应的处理
3. 验证轮询超时后的错误显示

### 4. UI 交互测试
1. 验证任务消息正确显示
2. 检查进度条在有进度数据时的显示
3. 确认不同状态的视觉效果正确

## API 端点依赖

组件依赖以下API端点正常工作：

1. **POST /api/image-gen/sketch** - 提交异步任务
   - 返回格式：`{success: true, data: {taskId: string, status: "pending", message: string}}`

2. **GET /api/image-gen/sketch/status?taskId=xxx** - 查询任务状态
   - 返回格式：`{success: true, data: {taskId, status, progress?, results?, error?}}`

## 兼容性说明

- 保持了与现有历史任务功能的完全兼容
- 保留了所有原有的UI交互和功能
- 向后兼容同步API响应格式（如果服务器返回results而不是taskId）

## 性能优化

- 轮询间隔设为2秒，平衡了实时性和服务器负载
- 最大轮询次数限制防止无限轮询
- 网络错误时自动延长重试间隔
- 任务完成后立即停止轮询

## 下一步建议

1. 在开发环境中测试完整流程
2. 验证与现有历史记录功能的集成
3. 测试多任务并发处理
4. 考虑添加用户取消任务的功能
5. 监控轮询请求的服务器负载
