# 异步图片转素描流程演示

## 用户操作流程

### 1. 用户提交任务
```
用户点击"生成" → 组件立即显示任务卡片（状态：Pending）
```

### 2. 任务状态变化时间线

```
时间点 0s:  [Pending]     任务提交成功，显示黄色 Pending 状态
时间点 2s:  [Generating]  第一次轮询，状态变为 Generating，显示蓝色动画
时间点 4s:  [Generating]  继续轮询，可能显示进度百分比
时间点 6s:  [Generating]  持续轮询...
...
时间点 30s: [Completed]   任务完成，显示绿色完成状态和结果图片
```

## 代码执行流程

### handleGenerate 函数执行步骤

```typescript
1. 验证输入和用户权限
2. 创建本地任务对象（status: 'pending'）
3. 添加到任务列表显示
4. 调用 API 提交任务
5. 收到 taskId 后更新本地任务
6. 启动 pollTaskStatus 轮询
7. 设置 isGenerating = false
```

### pollTaskStatus 轮询逻辑

```typescript
1. 发送 GET 请求到 /api/image-gen/sketch/status?taskId=xxx
2. 解析响应状态
3. 更新本地任务状态和进度
4. 根据状态决定：
   - pending/generating: 继续轮询（2秒后）
   - completed: 处理结果，停止轮询
   - error: 显示错误，停止轮询
   - 超时: 显示超时错误，停止轮询
```

## UI 状态展示

### Pending 状态
```
🟡 黄色主题
⏰ 时钟图标
📝 "Pending" 文本
💬 消息："Submitting task..." 或服务器返回的消息
```

### Generating 状态
```
🔵 蓝色主题
⚡ 旋转加载图标
📊 进度条（如果有进度数据）
💬 消息：服务器返回的处理状态
```

### Completed 状态
```
🟢 绿色主题
✅ 完成图标
🖼️ 生成的图片结果
🔽 下载和预览按钮
```

### Error 状态
```
🔴 红色主题
❌ 错误图标
💬 错误消息
🚫 禁用的操作按钮
```

## 网络请求示例

### 1. 提交任务请求
```http
POST /api/image-gen/sketch
Content-Type: application/json

{
  "images": ["data:image/jpeg;base64,..."],
  "style": "pencil",
  "aspectRatio": "1:1",
  "creditsNeeded": 1
}
```

### 2. 提交任务响应
```json
{
  "success": true,
  "data": {
    "taskId": "fc2ec89b-1234-419a-9eac-0ec447ee43b4",
    "status": "pending",
    "message": "Task created successfully. Use the taskId to poll for results."
  }
}
```

### 3. 状态查询请求
```http
GET /api/image-gen/sketch/status?taskId=fc2ec89b-1234-419a-9eac-0ec447ee43b4
```

### 4. 状态查询响应（处理中）
```json
{
  "success": true,
  "data": {
    "taskId": "fc2ec89b-1234-419a-9eac-0ec447ee43b4",
    "status": "generating",
    "progress": 45,
    "message": "Processing image 1 of 1..."
  }
}
```

### 5. 状态查询响应（完成）
```json
{
  "success": true,
  "data": {
    "taskId": "fc2ec89b-1234-419a-9eac-0ec447ee43b4",
    "status": "completed",
    "progress": 100,
    "results": [
      {
        "id": "result_1",
        "imageUrl": "https://example.com/generated-sketch.png"
      }
    ]
  }
}
```

## 错误处理场景

### 网络错误
- 轮询请求失败时，延长重试间隔到4秒
- 最多重试150次后显示超时错误

### 服务器错误
- API返回错误状态时，立即停止轮询
- 显示服务器返回的错误消息

### 任务超时
- 5分钟后仍未完成，显示超时错误
- 建议用户重新提交任务

## 用户体验改进

1. **即时反馈**：任务提交后立即显示，无需等待
2. **实时更新**：状态和进度实时更新
3. **视觉反馈**：不同状态有明确的视觉区分
4. **错误处理**：友好的错误提示和重试机制
5. **性能优化**：合理的轮询间隔，避免过度请求
