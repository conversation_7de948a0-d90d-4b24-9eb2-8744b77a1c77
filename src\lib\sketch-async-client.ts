/**
 * 异步Sketch生成客户端
 * 提供异步提交任务和轮询结果的功能
 */

export interface SketchTask {
  taskId: string;
  status: 'pending' | 'generating' | 'completed' | 'error';
  progress?: number;
  message?: string;
  results?: Array<{
    id: string;
    imageUrl?: string;
    base64?: string;
  }>;
  error?: string;
}

export interface SketchRequest {
  images: string[];        // base64编码的图片数组
  style: string;          // sketch风格
  aspectRatio: string;    // 宽高比
  creditsNeeded?: number; // 需要的积分数量
}

export class SketchAsyncClient {
  private pollingInterval: number;
  private maxPollingAttempts: number;

  constructor(options?: {
    pollingInterval?: number;  // 轮询间隔，默认2秒
    maxPollingAttempts?: number; // 最大轮询次数，默认150次（5分钟）
  }) {
    this.pollingInterval = options?.pollingInterval || 2000;
    this.maxPollingAttempts = options?.maxPollingAttempts || 150;
  }

  /**
   * 提交异步任务
   */
  async submitTask(request: SketchRequest): Promise<{ taskId: string; status: string; message: string }> {
    const response = await fetch('/api/image-gen/sketch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to submit task');
    }

    return {
      taskId: result.data.taskId,
      status: result.data.status,
      message: result.data.message
    };
  }

  /**
   * 查询单个任务状态
   */
  async getTaskStatus(taskId: string): Promise<SketchTask> {
    const response = await fetch(`/api/image-gen/sketch/status?taskId=${encodeURIComponent(taskId)}`);
    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to get task status');
    }

    return {
      taskId: result.data.taskId,
      status: result.data.status,
      progress: result.data.progress,
      message: result.data.message,
      results: result.data.results,
      error: result.data.error
    };
  }

  /**
   * 批量查询任务状态
   */
  async getBatchTaskStatus(taskIds: string[]): Promise<SketchTask[]> {
    const response = await fetch('/api/image-gen/sketch/batch-status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ taskIds }),
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to get batch task status');
    }

    return result.data.tasks.map((task: any) => ({
      taskId: task.taskId,
      status: task.status,
      progress: task.progress,
      message: task.message,
      results: task.results,
      error: task.error
    }));
  }

  /**
   * 轮询任务直到完成
   */
  async pollTaskUntilComplete(
    taskId: string,
    onProgress?: (task: SketchTask) => void
  ): Promise<SketchTask> {
    let attempts = 0;

    while (attempts < this.maxPollingAttempts) {
      try {
        const task = await this.getTaskStatus(taskId);
        
        // 调用进度回调
        if (onProgress) {
          onProgress(task);
        }

        // 如果任务完成或失败，返回结果
        if (task.status === 'completed' || task.status === 'error') {
          return task;
        }

        // 等待下次轮询
        await new Promise(resolve => setTimeout(resolve, this.pollingInterval));
        attempts++;

      } catch (error) {
        console.error(`Polling attempt ${attempts + 1} failed:`, error);
        attempts++;
        
        // 如果是网络错误，等待更长时间再重试
        await new Promise(resolve => setTimeout(resolve, this.pollingInterval * 2));
      }
    }

    throw new Error(`Task polling timeout after ${this.maxPollingAttempts} attempts`);
  }

  /**
   * 提交任务并等待完成（一站式方法）
   */
  async generateSketch(
    request: SketchRequest,
    onProgress?: (task: SketchTask) => void
  ): Promise<SketchTask> {
    // 1. 提交任务
    const submission = await this.submitTask(request);
    
    // 2. 轮询直到完成
    return await this.pollTaskUntilComplete(submission.taskId, onProgress);
  }

  /**
   * 批量轮询多个任务
   */
  async pollMultipleTasksUntilComplete(
    taskIds: string[],
    onProgress?: (tasks: SketchTask[]) => void
  ): Promise<SketchTask[]> {
    let attempts = 0;
    let completedTasks: SketchTask[] = [];
    let pendingTaskIds = [...taskIds];

    while (attempts < this.maxPollingAttempts && pendingTaskIds.length > 0) {
      try {
        const tasks = await this.getBatchTaskStatus(pendingTaskIds);
        
        // 分离已完成和未完成的任务
        const completed = tasks.filter(task => 
          task.status === 'completed' || task.status === 'error'
        );
        const pending = tasks.filter(task => 
          task.status === 'pending' || task.status === 'generating'
        );

        // 添加已完成的任务
        completedTasks.push(...completed);
        
        // 更新待处理任务列表
        pendingTaskIds = pending.map(task => task.taskId);

        // 调用进度回调
        if (onProgress) {
          onProgress([...completedTasks, ...pending]);
        }

        // 如果所有任务都完成了，返回结果
        if (pendingTaskIds.length === 0) {
          return completedTasks;
        }

        // 等待下次轮询
        await new Promise(resolve => setTimeout(resolve, this.pollingInterval));
        attempts++;

      } catch (error) {
        console.error(`Batch polling attempt ${attempts + 1} failed:`, error);
        attempts++;
        
        // 如果是网络错误，等待更长时间再重试
        await new Promise(resolve => setTimeout(resolve, this.pollingInterval * 2));
      }
    }

    throw new Error(`Batch task polling timeout after ${this.maxPollingAttempts} attempts`);
  }
}

// 创建默认客户端实例
export const sketchAsyncClient = new SketchAsyncClient();

// 使用示例：
/*
// 1. 基本使用
const client = new SketchAsyncClient();

// 提交任务
const submission = await client.submitTask({
  images: ['base64...', 'base64...'],
  style: 'pencil',
  aspectRatio: '1:1'
});

// 轮询结果
const result = await client.pollTaskUntilComplete(submission.taskId, (task) => {
  console.log(`Progress: ${task.progress}% - ${task.message}`);
});

// 2. 一站式使用
const result = await client.generateSketch({
  images: ['base64...'],
  style: 'pencil',
  aspectRatio: '1:1'
}, (task) => {
  console.log(`Progress: ${task.progress}%`);
});

// 3. 批量处理
const taskIds = ['task1', 'task2', 'task3'];
const results = await client.pollMultipleTasksUntilComplete(taskIds, (tasks) => {
  console.log(`Completed: ${tasks.filter(t => t.status === 'completed').length}/${tasks.length}`);
});
*/
