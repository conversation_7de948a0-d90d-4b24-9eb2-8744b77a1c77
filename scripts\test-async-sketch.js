/**
 * 测试异步Sketch API的脚本
 * 使用方法：node scripts/test-async-sketch.js
 */

const BASE_URL = 'http://localhost:3000';

// 模拟base64图片数据（1x1像素的透明PNG）
const MOCK_BASE64_IMAGE = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

async function testAsyncSketchAPI() {
  console.log('🚀 开始测试异步Sketch API...\n');

  try {
    // 1. 测试提交任务
    console.log('📤 步骤1: 提交异步任务...');
    const submitResponse = await fetch(`${BASE_URL}/api/image-gen/sketch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 注意：在实际使用中需要添加认证头
      },
      body: JSON.stringify({
        images: [MOCK_BASE64_IMAGE],
        style: 'pencil',
        aspectRatio: '1:1',
        creditsNeeded: 1
      })
    });

    if (!submitResponse.ok) {
      throw new Error(`提交任务失败: ${submitResponse.status} ${submitResponse.statusText}`);
    }

    const submitResult = await submitResponse.json();
    console.log('✅ 任务提交成功:', submitResult);

    if (!submitResult.success) {
      throw new Error(`任务提交失败: ${submitResult.error}`);
    }

    const taskId = submitResult.data.taskId;
    console.log(`📋 任务ID: ${taskId}\n`);

    // 2. 测试轮询任务状态
    console.log('🔄 步骤2: 开始轮询任务状态...');
    let attempts = 0;
    const maxAttempts = 30; // 最多轮询30次（1分钟）
    const pollInterval = 2000; // 2秒间隔

    while (attempts < maxAttempts) {
      const statusResponse = await fetch(`${BASE_URL}/api/image-gen/sketch/status?taskId=${encodeURIComponent(taskId)}`);
      
      if (!statusResponse.ok) {
        console.warn(`⚠️  状态查询失败: ${statusResponse.status} ${statusResponse.statusText}`);
        attempts++;
        await sleep(pollInterval);
        continue;
      }

      const statusResult = await statusResponse.json();
      
      if (!statusResult.success) {
        console.warn(`⚠️  状态查询失败: ${statusResult.error}`);
        attempts++;
        await sleep(pollInterval);
        continue;
      }

      const task = statusResult.data;
      console.log(`📊 轮询 ${attempts + 1}: 状态=${task.status}, 进度=${task.progress}%, 消息=${task.message}`);

      // 检查任务是否完成
      if (task.status === 'completed') {
        console.log('🎉 任务完成！');
        console.log('📸 生成结果:', task.results);
        break;
      } else if (task.status === 'error') {
        console.error('❌ 任务失败:', task.error);
        break;
      }

      attempts++;
      await sleep(pollInterval);
    }

    if (attempts >= maxAttempts) {
      console.warn('⏰ 轮询超时，任务可能仍在处理中');
    }

    // 3. 测试批量状态查询
    console.log('\n🔍 步骤3: 测试批量状态查询...');
    const batchResponse = await fetch(`${BASE_URL}/api/image-gen/sketch/batch-status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        taskIds: [taskId]
      })
    });

    if (batchResponse.ok) {
      const batchResult = await batchResponse.json();
      console.log('✅ 批量查询成功:', batchResult);
    } else {
      console.warn(`⚠️  批量查询失败: ${batchResponse.status} ${batchResponse.statusText}`);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error);
  }

  console.log('\n🏁 测试完成');
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 运行测试
if (require.main === module) {
  testAsyncSketchAPI();
}

module.exports = { testAsyncSketchAPI };
