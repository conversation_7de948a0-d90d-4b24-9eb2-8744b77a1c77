/**
 * 异步Sketch生成器组件示例
 * 展示如何使用新的异步API和轮询机制
 */

import React, { useState, useCallback } from 'react';
import { SketchAsyncClient, SketchTask, SketchRequest } from '@/lib/sketch-async-client';

interface TaskProgress {
  taskId: string;
  status: string;
  progress: number;
  message: string;
  results?: Array<{
    id: string;
    imageUrl?: string;
  }>;
  error?: string;
}

export const AsyncSketchGenerator: React.FC = () => {
  const [tasks, setTasks] = useState<Map<string, TaskProgress>>(new Map());
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [client] = useState(() => new SketchAsyncClient({
    pollingInterval: 2000,
    maxPollingAttempts: 150
  }));

  // 更新任务状态
  const updateTask = useCallback((taskId: string, updates: Partial<TaskProgress>) => {
    setTasks(prev => {
      const newTasks = new Map(prev);
      const existing = newTasks.get(taskId) || { taskId, status: 'pending', progress: 0, message: '' };
      newTasks.set(taskId, { ...existing, ...updates });
      return newTasks;
    });
  }, []);

  // 提交异步任务
  const handleSubmitAsync = async (request: SketchRequest) => {
    setIsSubmitting(true);
    
    try {
      // 1. 提交任务
      const submission = await client.submitTask(request);
      
      // 2. 添加到任务列表
      updateTask(submission.taskId, {
        status: submission.status,
        message: submission.message,
        progress: 0
      });

      // 3. 开始轮询（不阻塞UI）
      client.pollTaskUntilComplete(
        submission.taskId,
        (task: SketchTask) => {
          updateTask(task.taskId, {
            status: task.status,
            progress: task.progress || 0,
            message: task.message || '',
            results: task.results,
            error: task.error
          });
        }
      ).catch(error => {
        console.error('轮询失败:', error);
        updateTask(submission.taskId, {
          status: 'error',
          error: error.message,
          message: '轮询超时或失败'
        });
      });

      return submission.taskId;

    } catch (error) {
      console.error('提交任务失败:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  // 示例：处理文件上传和转换
  const handleFileUpload = async (files: FileList) => {
    if (!files.length) return;

    try {
      // 转换文件为base64
      const base64Images = await Promise.all(
        Array.from(files).map(file => fileToBase64(file))
      );

      // 提交任务
      await handleSubmitAsync({
        images: base64Images,
        style: 'pencil',
        aspectRatio: '1:1',
        creditsNeeded: base64Images.length
      });

    } catch (error) {
      console.error('处理文件失败:', error);
      alert('处理文件失败: ' + (error as Error).message);
    }
  };

  // 清除已完成的任务
  const clearCompletedTasks = () => {
    setTasks(prev => {
      const newTasks = new Map();
      for (const [taskId, task] of prev) {
        if (task.status !== 'completed' && task.status !== 'error') {
          newTasks.set(taskId, task);
        }
      }
      return newTasks;
    });
  };

  const taskList = Array.from(tasks.values()).sort((a, b) => 
    new Date(b.taskId).getTime() - new Date(a.taskId).getTime()
  );

  return (
    <div className="async-sketch-generator p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6">异步Sketch生成器</h2>
      
      {/* 文件上传区域 */}
      <div className="upload-area mb-6">
        <label className="block text-sm font-medium mb-2">
          选择图片文件
        </label>
        <input
          type="file"
          multiple
          accept="image/*"
          onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
          disabled={isSubmitting}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
        />
        {isSubmitting && (
          <p className="text-sm text-blue-600 mt-2">正在提交任务...</p>
        )}
      </div>

      {/* 任务控制 */}
      {taskList.length > 0 && (
        <div className="task-controls mb-4">
          <button
            onClick={clearCompletedTasks}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            清除已完成任务
          </button>
          <span className="ml-4 text-sm text-gray-600">
            总任务数: {taskList.length}
          </span>
        </div>
      )}

      {/* 任务列表 */}
      <div className="task-list space-y-4">
        {taskList.map(task => (
          <TaskCard key={task.taskId} task={task} />
        ))}
      </div>

      {taskList.length === 0 && (
        <div className="text-center text-gray-500 py-8">
          暂无任务，请上传图片开始生成
        </div>
      )}
    </div>
  );
};

// 任务卡片组件
const TaskCard: React.FC<{ task: TaskProgress }> = ({ task }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-600 bg-yellow-50';
      case 'generating': return 'text-blue-600 bg-blue-50';
      case 'completed': return 'text-green-600 bg-green-50';
      case 'error': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return '⏳';
      case 'generating': return '🔄';
      case 'completed': return '✅';
      case 'error': return '❌';
      default: return '❓';
    }
  };

  return (
    <div className="task-card border rounded-lg p-4 bg-white shadow-sm">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{getStatusIcon(task.status)}</span>
          <span className={`px-2 py-1 rounded text-sm font-medium ${getStatusColor(task.status)}`}>
            {task.status.toUpperCase()}
          </span>
        </div>
        <span className="text-xs text-gray-500">
          {task.taskId.slice(0, 8)}...
        </span>
      </div>

      {/* 进度条 */}
      {task.status === 'generating' && (
        <div className="mb-2">
          <div className="flex justify-between text-sm mb-1">
            <span>进度</span>
            <span>{task.progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${task.progress}%` }}
            />
          </div>
        </div>
      )}

      {/* 状态消息 */}
      <p className="text-sm text-gray-600 mb-2">{task.message}</p>

      {/* 错误信息 */}
      {task.error && (
        <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
          错误: {task.error}
        </p>
      )}

      {/* 结果展示 */}
      {task.results && task.results.length > 0 && (
        <div className="results mt-3">
          <h4 className="text-sm font-medium mb-2">生成结果:</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {task.results.map(result => (
              <div key={result.id} className="result-item">
                {result.imageUrl ? (
                  <img
                    src={result.imageUrl}
                    alt="Generated sketch"
                    className="w-full h-24 object-cover rounded border"
                  />
                ) : (
                  <div className="w-full h-24 bg-gray-100 rounded border flex items-center justify-center">
                    <span className="text-gray-400 text-xs">加载中...</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// 工具函数：文件转base64
function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

export default AsyncSketchGenerator;
