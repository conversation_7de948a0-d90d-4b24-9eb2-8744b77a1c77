import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { getUserUuid } from '@/services/user';
import { findSketchTaskByUuid, getSketchResultsByTaskUuid } from '@/models/sketch';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const revalidate = 0;

interface StatusResponse {
  success: boolean;
  data?: {
    taskId: string;
    status: 'pending' | 'generating' | 'completed' | 'error';
    message?: string;
    progress?: number;
    results?: Array<{
      id: string;
      imageUrl: string;
      originalImageUrl?: string;
    }>;
  };
  error?: string;
}

export async function GET(request: NextRequest) {
  try {
    // 获取用户认证信息
    const session = await auth();
    const userUuid = session?.user?.uuid || await getUserUuid();
    
    if (!userUuid || userUuid === 'anonymous') {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as StatusResponse, { status: 401 });
    }

    // 获取任务ID
    const { searchParams } = new URL(request.url);
    const taskId = searchParams.get('taskId');
    
    if (!taskId) {
      return NextResponse.json({
        success: false,
        error: 'Task ID is required'
      } as StatusResponse, { status: 400 });
    }

    // 查找任务
    const task = await findSketchTaskByUuid(taskId);
    
    if (!task) {
      return NextResponse.json({
        success: false,
        error: 'Task not found'
      } as StatusResponse, { status: 404 });
    }

    // 验证任务所有权
    if (task.user_uuid !== userUuid) {
      return NextResponse.json({
        success: false,
        error: 'Access denied'
      } as StatusResponse, { status: 403 });
    }

    // 验证任务类型
    if (task.task_type !== 'sketch-to-image') {
      return NextResponse.json({
        success: false,
        error: 'Invalid task type'
      } as StatusResponse, { status: 400 });
    }

    let results: Array<{
      id: string;
      imageUrl: string;
      originalImageUrl?: string;
    }> = [];

    // 如果任务已完成，获取结果
    if (task.status === 'completed') {
      const taskResults = await getSketchResultsByTaskUuid(taskId);
      results = taskResults.map(result => ({
        id: result.uuid,
        imageUrl: result.result_image_url,
        originalImageUrl: result.original_image_url
      }));
    }

    // 计算进度
    let progress = 0;
    if (task.status === 'completed') {
      progress = 100;
    } else if (task.status === 'generating') {
      // 基于完成数量计算进度
      const completedCount = task.completed_count || 0;
      const totalCount = task.original_image_count || 1;
      progress = Math.min(Math.round((completedCount / totalCount) * 100), 95);
    }

    // 生成状态消息
    let message = '';
    switch (task.status) {
      case 'pending':
        message = 'Task is waiting to be processed...';
        break;
      case 'generating':
        message = `Processing ${task.completed_count || 0} of ${task.original_image_count || 1} images...`;
        break;
      case 'completed':
        message = 'Task completed successfully';
        break;
      case 'error':
        message = task.error_message || 'Task failed with unknown error';
        break;
    }

    return NextResponse.json({
      success: true,
      data: {
        taskId: task.uuid,
        status: task.status as 'pending' | 'generating' | 'completed' | 'error',
        message,
        progress,
        results
      }
    } as StatusResponse);

  } catch (error) {
    console.error('Status check error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    } as StatusResponse, { status: 500 });
  }
}
