import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { findSketchTaskByUuid, findSketchResultsByTaskUuid } from '@/models/sketch';
import { getUserUuid } from '@/services/user';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const revalidate = 0;

interface TaskStatusResponse {
  success: boolean;
  data?: {
    taskId: string;
    status: string;      // pending/generating/completed/error
    progress?: number;   // 0-100
    message?: string;
    results?: Array<{
      id: string;
      imageUrl?: string;
      base64?: string;
    }>;
    error?: string;
  };
  error?: string;
}

// 格式化URL，确保包含https://前缀
const formatUrl = (url: string | null): string => {
  if (!url) return '';
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  return `https://${url}`;
};

export async function GET(request: NextRequest) {
  try {
    // 获取用户认证信息
    const session = await auth();
    const userUuid = session?.user?.uuid || await getUserUuid();

    if (!userUuid || userUuid === 'anonymous') {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as TaskStatusResponse, { status: 401 });
    }

    // 获取任务ID
    const { searchParams } = new URL(request.url);
    const taskId = searchParams.get('taskId');

    if (!taskId) {
      return NextResponse.json({
        success: false,
        error: 'Task ID is required'
      } as TaskStatusResponse, { status: 400 });
    }

    // 查询任务信息
    const task = await findSketchTaskByUuid(taskId);

    if (!task) {
      return NextResponse.json({
        success: false,
        error: 'Task not found'
      } as TaskStatusResponse, { status: 404 });
    }

    // 验证任务所有权
    if (task.user_uuid !== userUuid) {
      return NextResponse.json({
        success: false,
        error: 'Access denied'
      } as TaskStatusResponse, { status: 403 });
    }

    // 计算进度
    let progress = 0;
    if (task.status === 'completed') {
      progress = 100;
    } else if (task.status === 'generating') {
      // 基于已完成数量计算进度
      progress = Math.round((task.completed_count || 0) / (task.original_image_count || 1) * 100);
    } else if (task.status === 'pending') {
      progress = 0;
    }

    // 如果任务已完成，获取结果
    let results: Array<{ id: string; imageUrl?: string; base64?: string }> = [];
    if (task.status === 'completed') {
      const taskResults = await findSketchResultsByTaskUuid(taskId);
      if (taskResults) {
        results = taskResults.map((result, index) => ({
          id: result.uuid,
          imageUrl: formatUrl(result.result_image_url),
          // 不返回base64以减少响应大小
        }));
      }
    }

    // 构建响应
    const responseData: TaskStatusResponse = {
      success: true,
      data: {
        taskId: task.uuid,
        status: task.status,
        progress,
        message: getStatusMessage(task.status, progress),
        results: results.length > 0 ? results : undefined,
        error: task.error_message || undefined
      }
    };

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('Error fetching task status:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as TaskStatusResponse, { status: 500 });
  }
}

// 获取状态消息
function getStatusMessage(status: string, progress: number): string {
  switch (status) {
    case 'pending':
      return 'Task is queued and waiting to be processed';
    case 'generating':
      return `Processing images... ${progress}% complete`;
    case 'completed':
      return 'All images have been processed successfully';
    case 'error':
      return 'Task failed to complete';
    default:
      return 'Unknown status';
  }
}
