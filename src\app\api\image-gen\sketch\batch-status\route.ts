import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { findSketchResultsByTaskUuid } from '@/models/sketch';
import { getUserUuid } from '@/services/user';
import { db } from '@/db';
import { sketchTasks } from '@/db/schema';
import { eq, and, inArray } from 'drizzle-orm';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const revalidate = 0;

interface BatchTaskStatusResponse {
  success: boolean;
  data?: {
    tasks: Array<{
      taskId: string;
      status: string;      // pending/generating/completed/error
      progress?: number;   // 0-100
      message?: string;
      results?: Array<{
        id: string;
        imageUrl?: string;
      }>;
      error?: string;
    }>;
  };
  error?: string;
}

// 格式化URL，确保包含https://前缀
const formatUrl = (url: string | null): string => {
  if (!url) return '';
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  return `https://${url}`;
};

// 获取状态消息
function getStatusMessage(status: string, progress: number): string {
  switch (status) {
    case 'pending':
      return 'Task is queued and waiting to be processed';
    case 'generating':
      return `Processing images... ${progress}% complete`;
    case 'completed':
      return 'All images have been processed successfully';
    case 'error':
      return 'Task failed to complete';
    default:
      return 'Unknown status';
  }
}

export async function POST(request: NextRequest) {
  try {
    // 获取用户认证信息
    const session = await auth();
    const userUuid = session?.user?.uuid || await getUserUuid();

    if (!userUuid || userUuid === 'anonymous') {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as BatchTaskStatusResponse, { status: 401 });
    }

    // 获取任务ID列表
    const body = await request.json();
    const { taskIds } = body;

    if (!taskIds || !Array.isArray(taskIds) || taskIds.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Task IDs array is required'
      } as BatchTaskStatusResponse, { status: 400 });
    }

    if (taskIds.length > 50) {
      return NextResponse.json({
        success: false,
        error: 'Maximum 50 task IDs allowed per request'
      } as BatchTaskStatusResponse, { status: 400 });
    }

    // 批量查询任务信息
    const tasks = await db()
      .select()
      .from(sketchTasks)
      .where(and(
        eq(sketchTasks.user_uuid, userUuid),
        inArray(sketchTasks.uuid, taskIds)
      ));

    // 处理每个任务的状态
    const taskStatuses = await Promise.all(
      tasks.map(async (task) => {
        // 计算进度
        let progress = 0;
        if (task.status === 'completed') {
          progress = 100;
        } else if (task.status === 'generating') {
          // 基于已完成数量计算进度
          progress = Math.round((task.completed_count || 0) / (task.original_image_count || 1) * 100);
        } else if (task.status === 'pending') {
          progress = 0;
        }

        // 如果任务已完成，获取结果
        let results: Array<{ id: string; imageUrl?: string }> = [];
        if (task.status === 'completed') {
          const taskResults = await findSketchResultsByTaskUuid(task.uuid);
          if (taskResults) {
            results = taskResults.map((result) => ({
              id: result.uuid,
              imageUrl: formatUrl(result.result_image_url),
            }));
          }
        }

        return {
          taskId: task.uuid,
          status: task.status,
          progress,
          message: getStatusMessage(task.status, progress),
          results: results.length > 0 ? results : undefined,
          error: task.error_message || undefined
        };
      })
    );

    // 构建响应
    const responseData: BatchTaskStatusResponse = {
      success: true,
      data: {
        tasks: taskStatuses
      }
    };

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('Error fetching batch task status:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    } as BatchTaskStatusResponse, { status: 500 });
  }
}
