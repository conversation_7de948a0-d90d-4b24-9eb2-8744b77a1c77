import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { newStorage } from '@/lib/storage';
import { v4 as uuidv4 } from 'uuid';
import { 
  insertSketchTask, 
  insertSketchResults, 
  updateSketchTaskStatus,
  findSketchTaskByUuid
} from '@/models/sketch';
import { decreaseCredits, decreaseCreditsWithTransaction, CreditsTransType, getUserCredits } from '@/services/credit';
import { getUserUuid } from '@/services/user';
import { createSecurityMiddleware, validateSketchToImageRequest, logAuditEvent, getClientIP } from '@/lib/security';

export const runtime = 'nodejs';
export const maxDuration = 200; // 最大执行时间60秒

// 配置请求体大小限制（50MB）
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '50mb',
    },
  },
}

// 如果使用 App Router，添加这个配置
export const dynamic = 'force-dynamic';
export const revalidate = 0;

interface SketchToImageRequest {
  images: Array<{
    id: string;
    data: string;          // base64编码的图片数据
    filename: string;
  }> | string[];           // 支持两种格式：对象数组或字符串数组
  style: string;          // 图片风格
  aspectRatio: string;    // 宽高比
  creditsNeeded?: number; // 需要的积分数量
  creativity?: number;    // 创意度参数
}

interface SketchToImageResponse {
  success: boolean;
  data?: {
    taskId?: string;      // 任务ID
    status?: string;      // 任务状态 (pending/generating/completed/error)
    message?: string;     // 状态消息
    results?: Array<{     // 结果数组，异步模式下可能为空
      id: string;
      imageUrl?: string;
      base64?: string;
    }>;
  };
  error?: string;
}

// 风格到prompt的映射 - 将sketch转换为真实图片的提示词
const stylePrompts: Record<string, string> = {
  "architecture": "Render this sketch as a high-quality architectural photograph, with realistic textures, precise lines, and a professional architectural aesthetic.",
  "default": "Convert this sketch into a photorealistic image with natural lighting, high-resolution details, and accurate shading.",
  "photorealistic": "Turn this sketch into an ultra-photorealistic image, with detailed textures, soft natural lighting, and lifelike rendering quality.",
  "digitalart": "Transform this sketch into a contemporary digital painting, with vibrant colors, smooth gradients, and a modern digital art style.",
  "anime": "Turn this sketch into anime-style artwork, featuring bold outlines, expressive characters, vibrant color palettes, and a clean Japanese manga aesthetic.",
  "interiordesign": "Convert this sketch into a realistic interior design visualization, showcasing modern furniture, professional lighting, and clean spatial layout.",
  "3d": "Generate a 3D-rendered version of this sketch with realistic materials, accurate depth, and studio-quality lighting.",
  "pixar": "Render this sketch in Pixar-style animation, featuring soft 3D modeling, expressive cartoon characters, and vibrant, family-friendly colors.",
  "fantasy": "Transform this sketch into fantasy artwork, with magical elements, surreal lighting, mythical creatures, and an enchanted world atmosphere.",
  "rpg": "Convert this sketch into RPG game-style concept art, with detailed character design, fantasy environment, and immersive role-playing game aesthetics.",
  "comicbook": "Render this sketch in comic book style, using bold inking, dramatic poses, halftone shading, and dynamic storytelling composition.",
  "clay": "Reimagine this sketch as a clay sculpture style image, with handcrafted textures, soft modeling, and a tangible, tactile appearance.",
  "vectorart": "Convert this sketch into clean vector art, using geometric shapes, solid color fills, and minimal detail for a scalable graphic look.",
  "minimalist": "Transform this sketch into minimalist artwork, using simple forms, a limited color palette, and clean negative space.",
  "watercolor": "Render this sketch in watercolor painting style, with fluid brushstrokes, soft color transitions, and a handmade traditional look.",
  "oilpainting": "Convert this sketch into a classical oil painting style, with textured brushwork, rich colors, and depth from layered paint effects.",
  "gta": "Transform this sketch into GTA-style artwork, featuring urban themes, gritty realism, bold outlines, and stylized digital painting textures.",
  "minecraft": "Render this sketch in Minecraft voxel style, using cubic blocks, pixel textures, and a stylized 3D low-res aesthetic."
};

// 宽高比到尺寸的映射
const aspectRatioSizes: Record<string, string> = {
  "1:1": "1024x1024",
  "3:2": "1536x1024", 
  "2:3": "1024x1536"
};

// 将base64转换为Blob
function base64ToBlob(base64: string): Blob {
  // 移除data:image/...;base64,前缀（如果存在）
  const base64Data = base64.replace(/^data:image\/[a-z]+;base64,/, '');
  const byteCharacters = atob(base64Data);
  const byteNumbers = new Array(byteCharacters.length);
  
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: 'image/png' });
}

// 将base64转换为Buffer
function base64ToBuffer(base64: string): Buffer {
  const base64Data = base64.replace(/^data:image\/[a-z]+;base64,/, '');
  return Buffer.from(base64Data, 'base64');
}

// 上传图片到R2存储
async function uploadImageToR2(
  base64: string,
  userUuid: string,
  taskUuid: string,
  filename: string
): Promise<string> {
  try {
    const storage = newStorage();
    const buffer = base64ToBuffer(base64);
    const key = `sketch-to-image/${userUuid}/${taskUuid}/${filename}`;
    
    const result = await storage.uploadFile({
      body: buffer,
      key: key,
      contentType: 'image/png',
    });
    
    return result.url || result.location || '';
  } catch (error) {
    console.error('Error uploading to R2:', error);
    throw error;
  }
}

// 单张图片处理函数
async function processImage(
  imageBase64: string, 
  index: number, 
  prompt: string, 
  size: string, 
  apiKey: string, 
  apiBase: string
): Promise<{ id: string; imageUrl?: string; base64?: string }> {
  try {
    console.log(`Processing sketch-to-image ${index}, prompt: ${prompt}`);
    
    // 构建FormData（模拟multipart/form-data）
    const formData = new FormData();
    
    // 将base64转换为Blob并添加到FormData
    const imageBlob = base64ToBlob(imageBase64);
    formData.append('image', imageBlob, `sketch_${index}.png`);
    formData.append('model', 'gpt-image-1');
    formData.append('prompt', prompt);
    formData.append('n', '1');
    formData.append('size', size);
    formData.append('response_format', 'b64_json');

    console.log(`API Request for sketch-to-image ${index}:`, {
      url: `${apiBase}/images/edits`,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey.substring(0, 10)}...`
      },
      hasImageData: !!imageBlob,
      imageBlobSize: imageBlob.size
    });

    const response = await fetch(`${apiBase}/images/edits`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`
        // 注意：不要设置Content-Type，让fetch自动设置multipart/form-data
      },
      body: formData
    });

    console.log(`API Response for sketch-to-image ${index}: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`AI API error for sketch-to-image ${index}:`, {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      
      console.log(`Falling back to mock implementation for sketch-to-image ${index}`);
      // 如果AI API失败，使用模拟实现
      return await mockSketchToImageGeneration(imageBase64, index, prompt);
    }

    const aiResult = await response.json();
    console.log(`AI Result for sketch-to-image ${index}:`, {
      hasData: !!aiResult.data,
      dataLength: aiResult.data?.length || 0
    });
    
    if (aiResult.data && aiResult.data.length > 0) {
      const generatedImage = aiResult.data[0];
      return {
        id: `result_${index}_${Date.now()}`,
        base64: generatedImage.b64_json,
        imageUrl: generatedImage.url
      };
    } else {
      console.warn(`No data in AI result for sketch-to-image ${index}:`, aiResult);
      // fallback到模拟实现
      return await mockSketchToImageGeneration(imageBase64, index, prompt);
    }

  } catch (error) {
    console.error(`Error processing sketch-to-image ${index}:`, error);
    // fallback到模拟实现
    return await mockSketchToImageGeneration(imageBase64, index, prompt);
  }
}

// 模拟sketch-to-image生成 - 临时实现
async function mockSketchToImageGeneration(
  imageBase64: string, 
  index: number, 
  prompt: string
): Promise<{ id: string; imageUrl?: string; base64?: string }> {
  console.log(`Using mock implementation for sketch-to-image ${index}`);
  
  // 模拟处理时间
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
  
  // 这里可以返回一些预设的示例图片base64，或者做简单的图像处理
  // 暂时返回原图（在实际应用中，你可以替换为预设的realistic样例）
  return {
    id: `result_${index}_${Date.now()}`,
    base64: imageBase64  // 临时返回原图
  };
}

export async function POST(request: NextRequest) {
  try {
    // 获取用户认证信息
    const session = await auth();
    const userUuid = session?.user?.uuid || await getUserUuid(); // 确保获取到用户UUID
    
    if (!userUuid || userUuid === 'anonymous') {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      } as SketchToImageResponse, { status: 401 });
    }
    
    const body: SketchToImageRequest = await request.json();
    const { images, style, aspectRatio, creditsNeeded, creativity } = body;

    // 🔥 简化安全检查以提高性能
    const validation = validateSketchToImageRequest(body);
    if (!validation.valid) {
      return NextResponse.json({
        success: false,
        error: validation.error || 'Invalid request parameters'
      } as SketchToImageResponse, { status: 400 });
    }

    // 规范化 images 数据格式
    let imageDataArray: string[] = [];
    if (images.length > 0 && typeof images[0] === 'object' && 'data' in images[0]) {
      // 新格式：对象数组
      imageDataArray = (images as Array<{id: string; data: string; filename: string}>).map(img => img.data);
    } else {
      // 旧格式：字符串数组
      imageDataArray = images as string[];
    }

    // console.log('Received sketch-to-image request:', {
    //   userUuid,
    //   imageCount: imageDataArray.length,
    //   style,
    //   aspectRatio,
    //   creativity,
    //   firstImageSize: imageDataArray[0]?.length || 0
    // });

    // 验证输入参数
    if (!images || !Array.isArray(images) || images.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'At least one sketch image is required'
      } as SketchToImageResponse, { status: 400 });
    }

    if (imageDataArray.length > 10) {
      return NextResponse.json({
        success: false,
        error: 'Maximum 10 images allowed per request'
      } as SketchToImageResponse, { status: 400 });
    }

    if (!style || !stylePrompts[style]) {
      return NextResponse.json({
        success: false,
        error: 'Invalid style provided'
      } as SketchToImageResponse, { status: 400 });
    }

    // 🔥 新增：验证 creditsNeeded 参数
    const creditsToDeduct = creditsNeeded || imageDataArray.length;
    if (typeof creditsToDeduct !== 'number' || creditsToDeduct <= 0 || creditsToDeduct > 100) {
      return NextResponse.json({
        success: false,
        error: 'Invalid credits amount. Must be between 1 and 100.'
      } as SketchToImageResponse, { status: 400 });
    }

    // 确保积分需求与图片数量合理匹配（防止恶意请求）
    if (creditsToDeduct < imageDataArray.length || creditsToDeduct > imageDataArray.length * 5) {
      return NextResponse.json({
        success: false,
        error: 'Credits needed must be reasonable for the number of images'
      } as SketchToImageResponse, { status: 400 });
    }

    // 🔥 新增：预先检查用户积分余额
    const userCredits = await getUserCredits(userUuid);
    if (userCredits.left_credits < creditsToDeduct) {
      return NextResponse.json({
        success: false,
        error: `Insufficient credits. You have ${userCredits.left_credits} credits but need ${creditsToDeduct}.`
      } as SketchToImageResponse, { status: 402 });
    }

    // 🔥 新增：在处理开始前扣减积分，使用事务保护防止竞态条件
    try {
      await decreaseCreditsWithTransaction({
        user_uuid: userUuid,
        trans_type: CreditsTransType.Ping,
        credits: creditsToDeduct,
      });
      console.log(`Successfully deducted ${creditsToDeduct} credits for user ${userUuid} before processing with transaction protection`);
    } catch (creditError) {
      console.error('Failed to deduct credits before processing:', creditError);
      return NextResponse.json({
        success: false,
        error: 'Credit deduction failed. Please try again.'
      } as SketchToImageResponse, { status: 402 });
    }

    // 创建任务记录
    const taskUuid = uuidv4();
    const task = await insertSketchTask({
      uuid: taskUuid,
      user_uuid: userUuid,
      status: 'pending',  // 改为 pending 状态，表示任务已创建但尚未开始处理
      task_type: 'sketch-to-image',
      style: style,
      aspect_ratio: aspectRatio,
      original_image_count: imageDataArray.length,
      completed_count: 0,
    });

    if (!task) {
      console.error('Failed to create sketch-to-image task');
      return NextResponse.json({
        success: false,
        error: 'Failed to create task'
      } as SketchToImageResponse, { status: 500 });
    }

    // 🔥 异步处理：立即返回任务ID，后台处理图片
    // 启动后台处理任务（不等待完成）
    processSketchToImageTaskAsync(taskUuid, imageDataArray, style, aspectRatio, creativity, userUuid, creditsToDeduct, request)
      .catch(error => {
        console.error(`Background processing failed for task ${taskUuid}:`, error);
        // 更新任务状态为错误
        updateSketchTaskStatus(taskUuid, 'error', 0, error.message).catch(console.error);
      });

    // 立即返回任务ID，客户端可以用此ID轮询状态
    return NextResponse.json({
      success: true,
      data: {
        taskId: taskUuid,
        status: 'pending',
        message: 'Task created successfully. Use the taskId to poll for results.'
      }
    } as SketchToImageResponse);



  } catch (error) {
    console.error('Sketch-to-image generation error:', error);

    // 如果有任务ID，更新任务状态为错误
    const taskUuidFromError = (error as any)?.taskUuid;
    if (taskUuidFromError) {
      try {
        await updateSketchTaskStatus(taskUuidFromError, 'error', 0, 'Generation failed');
      } catch (updateError) {
        console.error('Failed to update task status to error:', updateError);
      }
    }

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    } as SketchToImageResponse, { status: 500 });
  }
}

// 🔥 异步处理函数 - 在后台处理sketch-to-image任务
async function processSketchToImageTaskAsync(
  taskUuid: string,
  imageDataArray: string[],
  style: string,
  aspectRatio: string,
  creativity: number | undefined,
  userUuid: string,
  creditsToDeduct: number,
  request: NextRequest
): Promise<void> {
  try {
    console.log(`Starting background processing for sketch-to-image task ${taskUuid}`);

    // 更新任务状态为 generating
    await updateSketchTaskStatus(taskUuid, 'generating', 0, 'Processing images...');

    // 获取环境变量
    const apiKey = process.env.TUZI_API_KEY;
    const apiBase = process.env.TUZI_API_BASE || 'https://api.tu-zi.com/v1';

    if (!apiKey) {
      throw new Error('TUZI_API_KEY not configured');
    }

    // 构建prompt，包含creativity参数
    const stylePrompt = stylePrompts[style];

    // 根据creativity值调整prompt
    let creativityModifier = "";
    if (creativity !== undefined) {
      if (creativity <= 20) {
        creativityModifier = ", stay very close to the original sketch, minimal interpretation";
      } else if (creativity <= 40) {
        creativityModifier = ", stay mostly faithful to the original sketch, slight artistic interpretation";
      } else if (creativity <= 60) {
        creativityModifier = ", balanced interpretation of the sketch, moderate creative enhancement";
      } else if (creativity <= 80) {
        creativityModifier = ", creative interpretation of the sketch, artistic enhancement while preserving main elements";
      } else {
        creativityModifier = ", highly creative interpretation, artistic freedom while maintaining core concept";
      }
    }

    const prompt = stylePrompt + creativityModifier;
    const size = aspectRatioSizes[aspectRatio] || '1024x1024';

    console.log('Generation parameters:', {
      stylePrompt,
      size,
      fullPrompt: prompt
    });

    // 并行处理所有图片
    const processingPromises = imageDataArray.map((imageBase64, index) =>
      processImage(imageBase64, index, prompt, size, apiKey, apiBase)
    );

    // 等待所有图片处理完成（使用allSettled确保即使某些失败也能继续）
    const results = await Promise.allSettled(processingPromises);

    // 提取成功的结果
    const finalResults = results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        console.error(`Failed to process sketch-to-image ${index}:`, result.reason);
        // 失败时返回原图作为fallback
        return {
          id: `result_${index}_${Date.now()}`,
          base64: imageDataArray[index]
        };
      }
    });

    console.log('Final sketch-to-image results:', {
      totalResults: finalResults.length,
      successCount: finalResults.filter((r, i) => r.base64 !== imageDataArray[i]).length
    });

    // 上传图片到R2并保存结果到数据库
    const resultRecords = [];

    for (let i = 0; i < finalResults.length; i++) {
      const result = finalResults[i];
      const originalImage = imageDataArray[i];

      try {
        // 上传原图
        const originalImageUrl = await uploadImageToR2(
          originalImage,
          userUuid,
          taskUuid,
          `original_sketch_${i}.png`
        );

        // 上传生成结果
        const resultImageUrl = await uploadImageToR2(
          result.base64 || originalImage,
          userUuid,
          taskUuid,
          `realistic_result_${i}.png`
        );

        // 创建结果记录
        const resultRecord = {
          uuid: uuidv4(),
          task_uuid: taskUuid,
          original_image_url: originalImageUrl,
          result_image_url: resultImageUrl,
          status: 'completed' as const,
          r2_key: `sketch-to-image/${userUuid}/${taskUuid}/realistic_result_${i}.png`,
          file_size: result.base64 ? Buffer.from(result.base64, 'base64').length : 0,
        };

        resultRecords.push(resultRecord);

      } catch (uploadError) {
        console.error(`Failed to upload sketch-to-image for index ${i}:`, uploadError);
        // 如果上传失败，至少保存基本记录
        const resultRecord = {
          uuid: uuidv4(),
          task_uuid: taskUuid,
          original_image_url: '',
          result_image_url: '',
          status: 'error' as const,
          r2_key: '',
          file_size: 0,
        };
        resultRecords.push(resultRecord);
      }
    }

    // 批量插入结果记录
    if (resultRecords.length > 0) {
      await insertSketchResults(resultRecords);
    }

    // 更新任务状态为完成
    await updateSketchTaskStatus(
      taskUuid,
      'completed',
      finalResults.length,
      'All images processed successfully'
    );

    console.log(`Background processing completed successfully for task ${taskUuid}`);

  } catch (error) {
    console.error(`Background processing failed for task ${taskUuid}:`, error);

    // 更新任务状态为错误
    await updateSketchTaskStatus(
      taskUuid,
      'error',
      0,
      error instanceof Error ? error.message : 'Unknown processing error'
    ).catch(console.error);

    throw error;
  }
}