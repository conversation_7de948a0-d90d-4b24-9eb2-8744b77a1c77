# 异步Sketch生成API文档

## 概述

新的异步Sketch生成API采用了异步+轮询的架构模式，提供更好的用户体验和系统性能：

- **异步提交**：立即返回任务ID，不阻塞客户端
- **轮询查询**：客户端可以轮询任务状态和进度
- **批量查询**：支持同时查询多个任务状态
- **进度跟踪**：实时显示处理进度

## API接口

### 1. 提交异步任务

**POST** `/api/image-gen/sketch`

提交图片转sketch任务，立即返回任务ID。

#### 请求体
```json
{
  "images": ["base64...", "base64..."],  // base64编码的图片数组
  "style": "pencil",                     // sketch风格
  "aspectRatio": "1:1",                  // 宽高比
  "creditsNeeded": 2                     // 需要的积分数量（可选）
}
```

#### 响应
```json
{
  "success": true,
  "data": {
    "taskId": "uuid-task-id",
    "status": "pending",
    "message": "Task created successfully. Use the taskId to poll for results."
  }
}
```

### 2. 查询单个任务状态

**GET** `/api/image-gen/sketch/status?taskId={taskId}`

查询指定任务的当前状态和进度。

#### 响应
```json
{
  "success": true,
  "data": {
    "taskId": "uuid-task-id",
    "status": "generating",              // pending/generating/completed/error
    "progress": 50,                      // 0-100
    "message": "Processing images... 50% complete",
    "results": [                         // 仅在completed状态时返回
      {
        "id": "result-id",
        "imageUrl": "https://..."
      }
    ],
    "error": "error message"             // 仅在error状态时返回
  }
}
```

### 3. 批量查询任务状态

**POST** `/api/image-gen/sketch/batch-status`

同时查询多个任务的状态，适用于批量处理场景。

#### 请求体
```json
{
  "taskIds": ["task-id-1", "task-id-2", "task-id-3"]
}
```

#### 响应
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "taskId": "task-id-1",
        "status": "completed",
        "progress": 100,
        "message": "All images have been processed successfully",
        "results": [...]
      },
      {
        "taskId": "task-id-2",
        "status": "generating",
        "progress": 30,
        "message": "Processing images... 30% complete"
      }
    ]
  }
}
```

## 任务状态说明

| 状态 | 描述 |
|------|------|
| `pending` | 任务已创建，等待处理 |
| `generating` | 正在处理图片 |
| `completed` | 所有图片处理完成 |
| `error` | 任务处理失败 |

## 客户端使用示例

### 1. 使用提供的客户端库

```typescript
import { SketchAsyncClient } from '@/lib/sketch-async-client';

const client = new SketchAsyncClient({
  pollingInterval: 2000,    // 2秒轮询一次
  maxPollingAttempts: 150   // 最多轮询150次（5分钟）
});

// 方式1：分步操作
const submission = await client.submitTask({
  images: ['base64...'],
  style: 'pencil',
  aspectRatio: '1:1'
});

const result = await client.pollTaskUntilComplete(
  submission.taskId,
  (task) => {
    console.log(`进度: ${task.progress}% - ${task.message}`);
  }
);

// 方式2：一站式操作
const result = await client.generateSketch({
  images: ['base64...'],
  style: 'pencil',
  aspectRatio: '1:1'
}, (task) => {
  console.log(`进度: ${task.progress}%`);
});
```

### 2. 直接使用API

```typescript
// 1. 提交任务
const submitResponse = await fetch('/api/image-gen/sketch', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    images: ['base64...'],
    style: 'pencil',
    aspectRatio: '1:1'
  })
});

const { data: { taskId } } = await submitResponse.json();

// 2. 轮询状态
const pollStatus = async () => {
  const response = await fetch(`/api/image-gen/sketch/status?taskId=${taskId}`);
  const { data } = await response.json();
  
  if (data.status === 'completed') {
    console.log('任务完成！', data.results);
    return data;
  } else if (data.status === 'error') {
    console.error('任务失败：', data.error);
    return data;
  } else {
    console.log(`进度: ${data.progress}% - ${data.message}`);
    setTimeout(pollStatus, 2000); // 2秒后再次查询
  }
};

pollStatus();
```

### 3. React Hook示例

```typescript
import { useState, useEffect } from 'react';
import { SketchAsyncClient } from '@/lib/sketch-async-client';

export function useAsyncSketch() {
  const [tasks, setTasks] = useState<Map<string, SketchTask>>(new Map());
  const client = new SketchAsyncClient();

  const submitTask = async (request: SketchRequest) => {
    const submission = await client.submitTask(request);
    
    // 添加到任务列表
    setTasks(prev => new Map(prev).set(submission.taskId, {
      taskId: submission.taskId,
      status: 'pending',
      progress: 0,
      message: submission.message
    }));

    // 开始轮询
    client.pollTaskUntilComplete(submission.taskId, (task) => {
      setTasks(prev => new Map(prev).set(task.taskId, task));
    });

    return submission.taskId;
  };

  return {
    tasks: Array.from(tasks.values()),
    submitTask
  };
}
```

## 最佳实践

1. **合理设置轮询间隔**：建议2-5秒，避免过于频繁的请求
2. **设置超时机制**：避免无限轮询，建议最多5-10分钟
3. **错误处理**：妥善处理网络错误和任务失败
4. **批量处理**：对于多个任务，使用批量查询接口提高效率
5. **用户体验**：显示进度条和状态信息，让用户了解处理进度

## 迁移指南

从同步API迁移到异步API：

### 原同步方式
```typescript
const response = await fetch('/api/image-gen/sketch', { ... });
const result = await response.json();
// 直接获得结果
console.log(result.data.results);
```

### 新异步方式
```typescript
const client = new SketchAsyncClient();
const result = await client.generateSketch(request, (task) => {
  // 显示进度
  updateProgress(task.progress, task.message);
});
// 获得结果
console.log(result.results);
```

## 性能优势

- **响应速度**：立即返回任务ID，不需要等待处理完成
- **并发处理**：支持多个任务同时处理
- **资源优化**：避免长时间占用HTTP连接
- **用户体验**：实时进度反馈，可以取消或暂停操作
